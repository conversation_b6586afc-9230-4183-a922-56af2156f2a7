#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_event.h>
#include <esp_bt.h>

#include "application.h"
#include "system_info.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"

#define TAG "main"

// 声明来自ble_scan.c的函数
extern "C" {
    void ble_app_on_sync(void);
    void host_task(void *param);
}

// 全局蓝牙初始化函数
static bool bluetooth_init(void)
{
    ESP_LOGI(TAG, "Initializing Bluetooth");

    // 1. 释放经典蓝牙内存（如果不使用经典蓝牙）
    esp_err_t ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "Bluetooth controller release classic bt memory failed: %s", esp_err_to_name(ret));
        return false;
    }

    // 2. 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller init failed: %s", esp_err_to_name(ret));
        return false;
    }

    // 3. 启用蓝牙控制器
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller enable failed: %s", esp_err_to_name(ret));
        return false;
    }

    // 4. 初始化NimBLE主机栈
    ret = nimble_port_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init nimble: %s", esp_err_to_name(ret));
        return false;
    }

    // 5. 设置设备名称和GAP服务
    ble_svc_gap_device_name_set("xiaozhi-BLE-Device");
    ble_svc_gap_init();
    ble_hs_cfg.sync_cb = ble_app_on_sync;

    // 6. 启动NimBLE主机任务
    nimble_port_freertos_init(host_task);

    ESP_LOGI(TAG, "Bluetooth initialized successfully");
    return true;
}

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize Bluetooth
    if (!bluetooth_init()) {
        ESP_LOGE(TAG, "Bluetooth initialization failed");
        // Continue without Bluetooth functionality
    }

    // Launch the application
    auto& app = Application::GetInstance();
    app.Start();
    app.MainEventLoop();
}
