#include "ble_scanner.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <cJSON.h>

// NimBLE includes
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "esp_bt.h"

static const char* TAG = "BLE_SCANNER";

// 静态实例指针，用于回调函数访问
static BleScanner* g_scanner_instance = nullptr;

std::string BleDeviceInfo::to_json() const {
    cJSON* json = cJSON_CreateObject();
    
    cJSON_AddStringToObject(json, "address", address.c_str());
    cJSON_AddNumberToObject(json, "addr_type", addr_type);
    cJSON_AddNumberToObject(json, "rssi", rssi);
    cJSON_AddStringToObject(json, "name", name.c_str());
    cJSON_AddBoolToObject(json, "connectable", connectable);
    cJSON_AddNumberToObject(json, "last_seen", last_seen);
    
    // 添加服务UUID数组
    if (!service_uuids.empty()) {
        cJSON* uuids_array = cJSON_CreateArray();
        for (uint16_t uuid : service_uuids) {
            cJSON_AddItemToArray(uuids_array, cJSON_CreateNumber(uuid));
        }
        cJSON_AddItemToObject(json, "service_uuids", uuids_array);
    }
    
    // 添加制造商数据
    if (!manufacturer_data.empty()) {
        cJSON_AddStringToObject(json, "manufacturer_data", manufacturer_data.c_str());
    }
    
    char* json_str = cJSON_PrintUnformatted(json);
    std::string result(json_str);
    cJSON_free(json_str);
    cJSON_Delete(json);
    
    return result;
}

BleScanner::BleScanner() 
    : initialized_(false)
    , is_scanning_(false)
    , scan_start_time_(0)
    , scan_duration_(0) {
    g_scanner_instance = this;
}

BleScanner::~BleScanner() {
    if (is_scanning_) {
        StopScan();
    }
    g_scanner_instance = nullptr;
}

bool BleScanner::Initialize() {
    if (initialized_) {
        return true;
    }

    ESP_LOGI(TAG, "Initializing BLE Scanner");

    // 只需要检查NimBLE主机是否已启用
    int timeout = 50; // 5秒超时
    while (!ble_hs_is_enabled() && timeout > 0) {
        vTaskDelay(pdMS_TO_TICKS(100));
        timeout--;
    }

    if (!ble_hs_is_enabled()) {
        ESP_LOGE(TAG, "NimBLE host not enabled; please call bluetooth_init() at startup");
        return false;
    }

    initialized_ = true;
    ESP_LOGI(TAG, "BLE Scanner initialized successfully");
    return true;
}

bool BleScanner::StartScan(uint32_t duration_ms, bool filter_duplicates) {
    if (!initialized_) {
        ESP_LOGE(TAG, "BLE Scanner not initialized");
        return false;
    }

    if (is_scanning_) {
        ESP_LOGW(TAG, "Already scanning, stopping previous scan");
        StopScan();
    }

    ESP_LOGI(TAG, "Starting BLE scan for %lu ms", duration_ms);

    uint8_t own_addr_type;
    struct ble_gap_disc_params disc_params = {0};
    int rc;

    // 获取地址类型
    rc = ble_hs_id_infer_auto(0, &own_addr_type);
    if (rc != 0) {
        ESP_LOGE(TAG, "Error determining address type; rc=%d", rc);
        return false;
    }

    // 配置扫描参数
    disc_params.filter_duplicates = filter_duplicates ? 1 : 0;
    disc_params.passive = 1;  // 被动扫描
    disc_params.itvl = 0;     // 使用默认间隔
    disc_params.window = 0;   // 使用默认窗口
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 开始扫描
    uint32_t scan_time = (duration_ms == 0) ? BLE_HS_FOREVER : duration_ms;
    rc = ble_gap_disc(own_addr_type, scan_time, &disc_params, gap_event_handler, nullptr);
    
    if (rc != 0) {
        ESP_LOGE(TAG, "Error initiating GAP discovery procedure; rc=%d", rc);
        return false;
    }

    is_scanning_ = true;
    scan_start_time_ = esp_timer_get_time() / 1000; // 转换为毫秒
    scan_duration_ = duration_ms;
    
    ESP_LOGI(TAG, "BLE scan started successfully");
    return true;
}

bool BleScanner::StopScan() {
    if (!is_scanning_) {
        return true;
    }

    ESP_LOGI(TAG, "Stopping BLE scan");
    
    int rc = ble_gap_disc_cancel();
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to stop scan; rc=%d", rc);
        return false;
    }

    is_scanning_ = false;
    ESP_LOGI(TAG, "BLE scan stopped");
    return true;
}

std::string BleScanner::GetScannedDevices() {
    std::lock_guard<std::mutex> lock(devices_mutex_);
    
    cJSON* json = cJSON_CreateObject();
    cJSON* devices_array = cJSON_CreateArray();
    
    for (const auto& device : discovered_devices_) {
        cJSON* device_json = cJSON_Parse(device.to_json().c_str());
        if (device_json) {
            cJSON_AddItemToArray(devices_array, device_json);
        }
    }
    
    cJSON_AddItemToObject(json, "devices", devices_array);
    cJSON_AddNumberToObject(json, "count", discovered_devices_.size());
    cJSON_AddBoolToObject(json, "scanning", is_scanning_);
    
    char* json_str = cJSON_PrintUnformatted(json);
    std::string result(json_str);
    cJSON_free(json_str);
    cJSON_Delete(json);
    
    return result;
}

void BleScanner::ClearResults() {
    std::lock_guard<std::mutex> lock(devices_mutex_);
    discovered_devices_.clear();
    ESP_LOGI(TAG, "Scan results cleared");
}

size_t BleScanner::GetDeviceCount() const {
    std::lock_guard<std::mutex> lock(devices_mutex_);
    return discovered_devices_.size();
}

int BleScanner::gap_event_handler(struct ble_gap_event *event, void *arg) {
    if (!g_scanner_instance) {
        return 0;
    }

    switch (event->type) {
    case BLE_GAP_EVENT_DISC:
        g_scanner_instance->handle_advertisement(&event->disc);
        return 0;

    case BLE_GAP_EVENT_DISC_COMPLETE:
        ESP_LOGI(TAG, "Discovery complete; reason=%d", event->disc_complete.reason);
        g_scanner_instance->is_scanning_ = false;
        return 0;

    default:
        return 0;
    }
}

void BleScanner::handle_advertisement(const struct ble_gap_disc_desc *disc) {
    BleDeviceInfo device;
    
    // 格式化地址
    device.address = format_address(disc->addr.val);
    device.addr_type = disc->addr.type;
    device.rssi = disc->rssi;
    device.last_seen = esp_timer_get_time() / 1000; // 转换为毫秒
    device.connectable = (disc->event_type == BLE_HCI_ADV_RPT_EVTYPE_ADV_IND ||
                         disc->event_type == BLE_HCI_ADV_RPT_EVTYPE_DIR_IND);

    // 解析广告数据
    parse_advertisement_data(disc->data, disc->length_data, device);

    // 添加到设备列表（检查是否已存在）
    std::lock_guard<std::mutex> lock(devices_mutex_);
    
    auto it = std::find_if(discovered_devices_.begin(), discovered_devices_.end(),
                          [&device](const BleDeviceInfo& existing) {
                              return existing.address == device.address;
                          });
    
    if (it != discovered_devices_.end()) {
        // 更新现有设备信息
        it->rssi = device.rssi;
        it->last_seen = device.last_seen;
        if (!device.name.empty() && it->name.empty()) {
            it->name = device.name;
        }
    } else {
        // 添加新设备
        discovered_devices_.push_back(device);
        ESP_LOGI(TAG, "Found device: %s, RSSI: %d, Name: %s",
                device.address.c_str(), device.rssi, device.name.c_str());
    }
}

void BleScanner::parse_advertisement_data(const uint8_t *data, uint8_t length, BleDeviceInfo &device) {
    struct ble_hs_adv_fields fields;
    int rc = ble_hs_adv_parse_fields(&fields, data, length);
    if (rc != 0) {
        return;
    }

    // 解析设备名称
    if (fields.name != nullptr && fields.name_len > 0) {
        device.name = std::string(reinterpret_cast<const char*>(fields.name), fields.name_len);
    }

    // 解析16位服务UUID
    for (int i = 0; i < fields.num_uuids16; i++) {
        device.service_uuids.push_back(ble_uuid_u16(&fields.uuids16[i].u));
    }

    // 解析制造商数据
    if (fields.mfg_data != nullptr && fields.mfg_data_len > 0) {
        std::stringstream ss;
        for (int i = 0; i < fields.mfg_data_len; i++) {
            ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(fields.mfg_data[i]);
        }
        device.manufacturer_data = ss.str();
    }
}

std::string BleScanner::format_address(const uint8_t addr[6]) {
    std::stringstream ss;
    for (int i = 5; i >= 0; i--) {
        ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(addr[i]);
        if (i > 0) ss << ":";
    }
    return ss.str();
}

void BleScanner::on_sync() {
    ESP_LOGI(TAG, "BLE host synchronized");
}

void BleScanner::on_reset(int reason) {
    ESP_LOGI(TAG, "BLE host reset; reason=%d", reason);
}
