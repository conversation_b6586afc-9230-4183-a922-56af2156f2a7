// NimBLE Client - Scan

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"

char *TAG = "BLE Client Scan";
uint8_t ble_addr_type;
void ble_app_scan(void);

// 用于MCP工具的扫描结果存储
#define MAX_SCAN_DEVICES 5
typedef struct {
    char address[18];  // MAC地址 "xx:xx:xx:xx:xx:xx"
    char name[32];     // 设备名称
    int rssi;          // 信号强度
} ble_device_t;

static ble_device_t scan_devices[MAX_SCAN_DEVICES];
static int scan_device_count = 0;
static bool scan_complete = false;

// BLE event handling
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    // NimBLE event discovery
    case BLE_GAP_EVENT_DISC:
        ESP_LOGI("GAP", "GAP EVENT DISCOVERY");
        ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data);
        if (fields.name_len > 0)
        {
            printf("Name: %.*s\n", fields.name_len, fields.name);
        }
        break;
    default:
        break;
    }
    return 0;
}

// MCP工具专用的事件处理函数
static int ble_mcp_scan_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    case BLE_GAP_EVENT_DISC:
        if (scan_device_count < MAX_SCAN_DEVICES) {
            // 格式化MAC地址
            snprintf(scan_devices[scan_device_count].address, sizeof(scan_devices[scan_device_count].address),
                    "%02x:%02x:%02x:%02x:%02x:%02x",
                    event->disc.addr.val[5], event->disc.addr.val[4], event->disc.addr.val[3],
                    event->disc.addr.val[2], event->disc.addr.val[1], event->disc.addr.val[0]);

            // 记录RSSI
            scan_devices[scan_device_count].rssi = event->disc.rssi;

            // 解析设备名称
            scan_devices[scan_device_count].name[0] = '\0';
            if (ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data) == 0) {
                if (fields.name_len > 0 && fields.name_len < sizeof(scan_devices[scan_device_count].name)) {
                    memcpy(scan_devices[scan_device_count].name, fields.name, fields.name_len);
                    scan_devices[scan_device_count].name[fields.name_len] = '\0';
                }
            }

            ESP_LOGI(TAG, "Found device: %s, RSSI: %d, Name: %s",
                    scan_devices[scan_device_count].address,
                    scan_devices[scan_device_count].rssi,
                    scan_devices[scan_device_count].name);

            scan_device_count++;
        }
        break;
    case BLE_GAP_EVENT_DISC_COMPLETE:
        ESP_LOGI(TAG, "Scan complete");
        scan_complete = true;
        break;
    default:
        break;
    }
    return 0;
}

void ble_app_scan(void)
{
    printf("Start scanning ...\n");

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    ble_gap_disc(ble_addr_type, BLE_HS_FOREVER, &disc_params, ble_gap_event, NULL);
}

// The application
void ble_app_on_sync(void)
{
    ble_hs_id_infer_auto(0, &ble_addr_type); // Determines the best address type automatically
    ble_app_scan();                          
}

// The infinite task
void host_task(void *param)
{
    nimble_port_run(); // This function will return only when nimble_port_stop() is executed
}

void scan_main()
{
    nvs_flash_init();                               // 1 - Initialize NVS flash using
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service
    ble_hs_cfg.sync_cb = ble_app_on_sync;           // 4 - Set application
    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}

// 初始化BLE控制器的函数
static bool ble_controller_init_once(void)
{
    static bool controller_initialized = false;
    if (controller_initialized) {
        return true;
    }

    ESP_LOGI(TAG, "Initializing BLE controller");

    // 1. 释放经典蓝牙内存（如果不使用经典蓝牙）
    esp_err_t ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller release classic bt memory failed: %s", esp_err_to_name(ret));
        return false;
    }

    // 2. 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller init failed: %s", esp_err_to_name(ret));
        return false;
    }

    // 3. 启用蓝牙控制器
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluetooth controller enable failed: %s", esp_err_to_name(ret));
        return false;
    }

    controller_initialized = true;
    ESP_LOGI(TAG, "BLE controller initialized successfully");
    return true;
}

// 初始化BLE的函数
static bool ble_init_once(void)
{
    static bool initialized = false;
    if (initialized) {
        return true;
    }

    // 首先初始化BLE控制器
    if (!ble_controller_init_once()) {
        ESP_LOGE(TAG, "BLE controller initialization failed");
        return false;
    }

    ESP_LOGI(TAG, "Initializing NimBLE host");

    // 初始化NimBLE主机栈
    esp_err_t ret = nimble_port_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init nimble: %s", esp_err_to_name(ret));
        return false;
    }

    // 设置设备名称和GAP服务
    ble_svc_gap_device_name_set("xiaozhi-Scan-Client");
    ble_svc_gap_init();
    ble_hs_cfg.sync_cb = ble_app_on_sync;

    // 启动NimBLE主机任务
    nimble_port_freertos_init(host_task);

    initialized = true;
    ESP_LOGI(TAG, "BLE initialized successfully");
    return true;
}

// MCP工具调用的扫描函数
char* ble_scan_once_json(int duration_ms)
{
    static char json_result[2048];

    // 重置扫描结果
    scan_device_count = 0;
    scan_complete = false;

    // 确保BLE已初始化
    if (!ble_init_once()) {
        ESP_LOGE(TAG, "BLE initialization failed");
        snprintf(json_result, sizeof(json_result), "{\"error\":\"BLE initialization failed\"}");
        return json_result;
    }

    // 获取地址类型
    if (ble_hs_id_infer_auto(0, &ble_addr_type) != 0) {
        ESP_LOGE(TAG, "Failed to infer address type");
        snprintf(json_result, sizeof(json_result), "{\"error\":\"Failed to infer address type\"}");
        return json_result;
    }

    // 配置扫描参数
    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 1;  // 被动扫描
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    ESP_LOGI(TAG, "Starting BLE scan for %d ms", duration_ms);

    // 开始扫描
    int rc = ble_gap_disc(ble_addr_type, duration_ms, &disc_params, ble_mcp_scan_event, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to start scan, rc=%d", rc);
        snprintf(json_result, sizeof(json_result), "{\"error\":\"Failed to start scan\"}");
        return json_result;
    }

    // 等待扫描完成
    int timeout = (duration_ms / 100) + 50; // 超时时间稍长一些
    while (!scan_complete && timeout > 0) {
        vTaskDelay(pdMS_TO_TICKS(100));
        timeout--;
    }

    if (!scan_complete) {
        ble_gap_disc_cancel();
        ESP_LOGW(TAG, "Scan timeout, stopping");
    }

    // 构建JSON结果
    int pos = snprintf(json_result, sizeof(json_result), "{\"devices\":[");

    for (int i = 0; i < scan_device_count && pos < sizeof(json_result) - 100; i++) {
        if (i > 0) {
            pos += snprintf(json_result + pos, sizeof(json_result) - pos, ",");
        }
        pos += snprintf(json_result + pos, sizeof(json_result) - pos,
                       "{\"address\":\"%s\",\"name\":\"%s\",\"rssi\":%d}",
                       scan_devices[i].address,
                       scan_devices[i].name,
                       scan_devices[i].rssi);
    }

    snprintf(json_result + pos, sizeof(json_result) - pos, "],\"count\":%d}", scan_device_count);

    ESP_LOGI(TAG, "Scan completed, found %d devices", scan_device_count);
    return json_result;
}